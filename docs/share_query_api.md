# Share Query API Documentation

## Overview

The Share Query API allows authenticated users to create a single shareable link for one or more queries. Multiple query IDs are stored as a JSON array in a single database record with one share UUID, making it easy to share a collection of related queries. Recipients can access all shared queries through a single public URL without requiring authentication.

## Endpoints

### Create Share

**POST** `/share/query`

Create a single shareable link for one or more queries. All query IDs are stored as a JSON array in a single database record with one share UUID.

#### Request

**Headers:**
- `auth0_sub`: User identifier (required)
- `Content-Type`: application/json

**Body:**
```json
{
  "query_ids": [1, 2, 3],
  "title": "Optional custom title",
  "description": "Optional description",
  "expires_in_days": 30
}
```

**Parameters:**
- `query_ids` (required): Array of query IDs to share (minimum 1 item)
- `title` (optional): Custom title for the share (max 255 characters)
- `description` (optional): Description for the share (max 1000 characters)
- `expires_in_days` (optional): Expiration in days (1-365)

#### Response

```json
{
  "success": true,
  "share_id": "uuid-string",
  "share_url": "https://addxgo.io/ai/uuid-string",
  "expires_at": "2024-01-15T10:30:00Z",
  "existing": false,
  "query_count": 3
}
```

**Response Fields:**
- `success`: Whether the share was created successfully
- `share_id`: UUID of the created share (if successful)
- `share_url`: Public URL for accessing the shared queries (if successful)
- `expires_at`: ISO timestamp when the share expires (if set)
- `existing`: Whether this exact set of queries was already shared
- `query_count`: Number of queries included in the share
- `error`: Error message (if failed)

### Get My Shares

**GET** `/share/my-shares`

Get all shares created by the authenticated user.

#### Request

**Headers:**
- `auth0_sub`: User identifier (required)

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10, max: 50)

### Revoke Share

**DELETE** `/share/{share_id}`

Revoke a shared query.

#### Request

**Headers:**
- `auth0_sub`: User identifier (required)

**Path Parameters:**
- `share_id`: UUID of the share to revoke

### Get Shared Query (Public)

**GET** `/pub/shared/{share_id}`

Get a shared query by share ID - no authentication required.

#### Request

**Path Parameters:**
- `share_id`: UUID of the shared query

## Examples

### Share Multiple Queries

```bash
curl -X POST http://localhost:8080/share/query \
  -H "Content-Type: application/json" \
  -H "auth0_sub: user123" \
  -d '{
    "query_ids": [1, 2, 3],
    "title": "My Analysis Results",
    "description": "Stock analysis for Tesla, Apple, and Google",
    "expires_in_days": 7
  }'
```

### Share Single Query

```bash
curl -X POST http://localhost:8080/share/query \
  -H "Content-Type: application/json" \
  -H "auth0_sub: user123" \
  -d '{
    "query_ids": [42],
    "title": "Tesla Analysis"
  }'
```

### Access Shared Query (Public)

```bash
curl http://localhost:8080/pub/shared/uuid-string
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad request (invalid parameters)
- `401`: Authentication required
- `404`: Query or share not found
- `410`: Share has expired or been revoked
- `500`: Internal server error

If any of the requested queries cannot be found or accessed, the entire operation will fail with an appropriate error message.
