#!/usr/bin/env python3
"""
Test script for the updated share/query API endpoint.
"""
import requests
import json

def test_share_query_api():
    """Test the new share/query API endpoint."""
    
    # Base URL - adjust as needed
    base_url = "http://localhost:8080"
    
    # Test data
    test_data = {
        "query_ids": [1, 2, 3],  # Example query IDs
        "title": "Test Share",
        "description": "Testing the new API endpoint",
        "expires_in_days": 30
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "auth0_sub": "test-user-123"  # Mock user ID
    }
    
    print("Testing share/query API endpoint with JSON array...")
    print(f"URL: {base_url}/share/query")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    
    try:
        # Make the request
        response = requests.post(
            f"{base_url}/share/query",
            json=test_data,
            headers=headers
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
            
            # Validate response structure
            assert "success" in response_data

            if response_data["success"]:
                assert "share_id" in response_data
                assert "share_url" in response_data
                assert "query_count" in response_data

                print(f"✅ Share created successfully!")
                print(f"  Share ID: {response_data['share_id']}")
                print(f"  Share URL: {response_data['share_url']}")
                print(f"  Query Count: {response_data['query_count']}")
                print(f"  Existing: {response_data.get('existing', False)}")
                if response_data.get("expires_at"):
                    print(f"  Expires At: {response_data['expires_at']}")
            else:
                print(f"❌ Share creation failed: {response_data.get('error')}")
            
            print("\n✅ API test completed successfully!")
            
        else:
            print(f"❌ API test failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the server is running on localhost:8080")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")

if __name__ == "__main__":
    test_share_query_api()
